/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import { Disposable } from '../../../../base/common/lifecycle.js';
import { ProxyChannel } from '../../../../base/parts/ipc/common/ipc.js';
import { METRICS_EVENT, METRICS_EVENT_TYPE, IMetricsService } from '../common/metricsService.js';
import { register<PERSON><PERSON><PERSON>, InstantiationType } from '../../../../platform/instantiation/common/extensions.js';
import { IMainProcessService } from '../../../../platform/ipc/common/mainProcessService.js';
import { localize2 } from '../../../../nls.js';
import { ServicesAccessor } from '../../../../editor/browser/editorExtensions.js';
import { registerAction2, Action2 } from '../../../../platform/actions/common/actions.js';
import { INotificationService } from '../../../../platform/notification/common/notification.js';
import { IProductService } from '../../../../platform/product/common/productService.js';
import { IStorageService, StorageScope, StorageTarget } from '../../../../platform/storage/common/storage.js';
import { isLinux, isMacintosh, isWindows } from '../../../../base/common/platform.js';
import { platform, arch } from '../../../../base/common/process.js';
import { ICodeseekUacLoginService, UserInfo } from '../common/uac/codeseekUacLoginService.js';


export class CodeseekMetricsService extends Disposable implements IMetricsService {

	readonly _serviceBrand: undefined;
	private readonly metricsService: IMetricsService;
	private VERSION_STORE_KEY = 'flowIdeVersion';
	private userInfo: UserInfo | undefined = undefined;

	constructor(
		@IProductService private readonly productService: IProductService,
		@IStorageService private readonly storageService: IStorageService,
		@ICodeseekUacLoginService private readonly codeseekUacLoginService: ICodeseekUacLoginService,
		@IMainProcessService mainProcessService: IMainProcessService,
	) {
		super();
		this.metricsService = ProxyChannel.toService<IMetricsService>(mainProcessService.getChannel('codeseek-channel-metrics'));
		this.checkUpgradeInfo();
	}

	capture(event: METRICS_EVENT_TYPE, params: Record<string, any>, userId?: string): void {
		this.metricsService.capture(event, params, this.userInfo?.userId);
	}

	/**
	 * 获取扩展版本信息
	 */
	private extensionInfo(): Record<string, any> {
		try {
			const builtinExtensions = this.productService.builtInExtensions || [];
			const extensionsInfo = builtinExtensions.map(ext => ({
				name: ext.name,
				version: ext.version,
				isBuiltin: true,
			}));
			return {
				extensions: extensionsInfo,
			};
		} catch (error) {
			console.error('[MetricsService] Failed to collect extension info:', error);
			return {
				extensions: {},
			};
		}
	}

	async getUserInfo() {
		try {
			this.userInfo = await this.codeseekUacLoginService.getUserInfo();
			console.log('[MetricsService] User info retrieved successfully:', this.userInfo);
		} catch (error) {
			console.error('[MetricsService] Failed to retrieve user info:', error);
		}
	}

	private osInfo(): Record<string, any> {
		try {
			const os = isWindows ? 'windows' : isMacintosh ? 'mac' : isLinux ? 'linux' : '';
			return { osInfo: { platform: platform, arch: arch, os } };
		}
		catch (e) {
			return { osInfo: { platform: '??', arch: '??', os: '??' } };
		}
	}

	private async checkUpgradeInfo(): Promise<void> {
		await this.getUserInfo();
		const preFlowIdeVersion = this.storageService.get(this.VERSION_STORE_KEY, StorageScope.APPLICATION, '');
		const flowIdeVersion = this.productService.productVersion;
		const os = this.osInfo();
		const extensions = this.extensionInfo();

		if (preFlowIdeVersion) {
			if (preFlowIdeVersion !== flowIdeVersion) {
				await this.capture(METRICS_EVENT.UPGRADE, { preFlowIdeVersion, flowIdeVersion, ...os, ...extensions });
				this.storageService.store(this.VERSION_STORE_KEY, flowIdeVersion, StorageScope.APPLICATION, StorageTarget.MACHINE);
			}
		} else {
			await this.capture(METRICS_EVENT.INSTALL, { flowIdeVersion, ...os, ...extensions });
			this.storageService.store(this.VERSION_STORE_KEY, flowIdeVersion, StorageScope.APPLICATION, StorageTarget.MACHINE);
		}
	}

	async getDebuggingProperties(): Promise<object> {
		const flowIdeVersion = this.productService.productVersion;
		const os = this.osInfo();
		const extensions = this.extensionInfo();
		return {
			version: flowIdeVersion,
			...os,
			...extensions
		};
	}
}

registerSingleton(IMetricsService, CodeseekMetricsService, InstantiationType.Eager);

// debugging action
registerAction2(class extends Action2 {
	constructor() {
		super({
			id: 'codeseekDebugInfo',
			f1: true,
			title: localize2('codeseekMetricsDebug', 'Codeseek: Log Debug Info'),
		});
	}
	async run(accessor: ServicesAccessor): Promise<void> {
		const metricsService = accessor.get(IMetricsService)
		const notifService = accessor.get(INotificationService)

		const debugProperties = await metricsService.getDebuggingProperties()
		console.log('Metrics:', debugProperties)
		notifService.info(`Codeseek Debug info:\n${JSON.stringify(debugProperties, null, 2)}`)
	}
});
